<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin Login - CRM Dashboard</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      .gradient-bg {
        background: linear-gradient(135deg, #00b2e5 0%, #10c0f3 100%);
      }
      .glass-effect {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }
    </style>
  </head>
  <body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- Login Card -->
      <div class="glass-effect rounded-2xl shadow-2xl p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div
            class="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center mb-4 p-3"
          >
            <img
              src="/static/logo.svg"
              alt="Company Logo"
              class="w-full h-full object-contain"
            />
          </div>
          <h1 class="text-2xl font-bold text-white mb-2">Admin Dashboard</h1>
          <p class="text-blue-100">Sign in to access the admin interface</p>
        </div>

        <!-- Error Message -->
        <div
          id="errorMessage"
          class="hidden mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg"
        >
          <i class="fas fa-exclamation-triangle mr-2"></i>
          <span id="errorText"></span>
        </div>

        <!-- Success Message -->
        <div
          id="successMessage"
          class="hidden mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg"
        >
          <i class="fas fa-check-circle mr-2"></i>
          <span id="successText"></span>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              <i class="fas fa-envelope mr-2"></i>Email Address
            </label>
            <input
              type="email"
              id="email"
              required
              class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent transition-all duration-200"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-white mb-2">
              <i class="fas fa-lock mr-2"></i>Password
            </label>
            <div class="relative">
              <input
                type="password"
                id="password"
                required
                class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent transition-all duration-200"
                placeholder="Enter your password"
              />
              <button
                type="button"
                id="togglePassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-200 hover:text-white transition-colors"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <button
            type="submit"
            id="loginBtn"
            class="w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-all duration-200 transform hover:scale-105"
          >
            <i class="fas fa-sign-in-alt mr-2"></i>
            <span id="loginBtnText">Sign In</span>
          </button>
        </form>

        <!-- Divider -->
        <div class="my-6 flex items-center">
          <div class="flex-1 border-t border-white border-opacity-30"></div>
          <span class="px-4 text-sm text-blue-100">or</span>
          <div class="flex-1 border-t border-white border-opacity-30"></div>
        </div>

        <!-- Google OAuth Button -->
        <button
          id="googleLoginBtn"
          class="w-full bg-white text-gray-700 font-semibold py-3 px-4 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-all duration-200 transform hover:scale-105 flex items-center justify-center"
        >
          <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Continue with Google
        </button>

        <!-- Footer -->
        <div class="mt-8 text-center">
          <p class="text-sm text-blue-100">Administrator access only</p>
        </div>
      </div>

      <!-- Loading Overlay -->
      <div
        id="loadingOverlay"
        class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
          <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
          <span class="text-lg font-medium">Signing in...</span>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const loginForm = document.getElementById("loginForm");
        const emailInput = document.getElementById("email");
        const passwordInput = document.getElementById("password");
        const togglePassword = document.getElementById("togglePassword");
        const loginBtn = document.getElementById("loginBtn");
        const loginBtnText = document.getElementById("loginBtnText");
        const googleLoginBtn = document.getElementById("googleLoginBtn");
        const errorMessage = document.getElementById("errorMessage");
        const successMessage = document.getElementById("successMessage");
        const errorText = document.getElementById("errorText");
        const successText = document.getElementById("successText");
        const loadingOverlay = document.getElementById("loadingOverlay");

        // Toggle password visibility
        togglePassword.addEventListener("click", function () {
          const type =
            passwordInput.getAttribute("type") === "password"
              ? "text"
              : "password";
          passwordInput.setAttribute("type", type);
          this.querySelector("i").classList.toggle("fa-eye");
          this.querySelector("i").classList.toggle("fa-eye-slash");
        });

        // Show/hide messages
        function showError(message) {
          errorText.textContent = message;
          errorMessage.classList.remove("hidden");
          successMessage.classList.add("hidden");
        }

        function showSuccess(message) {
          successText.textContent = message;
          successMessage.classList.remove("hidden");
          errorMessage.classList.add("hidden");
        }

        function hideMessages() {
          errorMessage.classList.add("hidden");
          successMessage.classList.add("hidden");
        }

        function showLoading(show) {
          if (show) {
            loadingOverlay.classList.remove("hidden");
            loginBtn.disabled = true;
            googleLoginBtn.disabled = true;
          } else {
            loadingOverlay.classList.add("hidden");
            loginBtn.disabled = false;
            googleLoginBtn.disabled = false;
          }
        }

        // Handle regular login
        loginForm.addEventListener("submit", async function (e) {
          e.preventDefault();
          hideMessages();
          showLoading(true);

          const email = emailInput.value;
          const password = passwordInput.value;

          try {
            const response = await fetch("/api/v1/login", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ email, password }),
            });

            const data = await response.json();

            // Handle the correct API response format with meta object
            if (data.meta && data.meta.status === "success") {
              // Store token
              localStorage.setItem("admin_token", data.data.token);
              showSuccess("Login successful! Redirecting...");

              // Redirect to admin dashboard
              setTimeout(() => {
                window.location.href = "/admin/dashboard";
              }, 1000);
            } else {
              // Handle error message from meta object
              const errorMsg =
                (data.meta && data.meta.message) ||
                data.message ||
                "Login failed";
              showError(errorMsg);
            }
          } catch (error) {
            showError("Network error. Please try again.");
          } finally {
            showLoading(false);
          }
        });

        // Handle Google OAuth
        googleLoginBtn.addEventListener("click", function () {
          showLoading(true);
          window.location.href = "/api/v1/google/login";
        });

        // Authentication temporarily disabled - redirect directly to dashboard
        console.log(
          "Login: authentication temporarily disabled, redirecting to dashboard"
        );
        window.location.href = "/admin/dashboard";
      });
    </script>
  </body>
</html>
