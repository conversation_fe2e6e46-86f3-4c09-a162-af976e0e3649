<!-- MAU Dashboard Content -->
<div class="space-y-8">
  <!-- Header -->
  <div
    class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-6 text-white"
  >
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h2 class="text-2xl font-bold mb-2">
          <i class="fas fa-users mr-3"></i>Monthly Active Users Dashboard
        </h2>
        <p class="text-blue-100">
          Track user engagement and activity patterns across your CRM system
        </p>
      </div>
      <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
        <button
          id="mauRefreshBtn"
          class="px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white border-opacity-20"
        >
          <i class="fas fa-sync-alt mr-2"></i>Refresh
        </button>
        <button
          id="mauExportBtn"
          class="px-6 py-3 bg-green-500 hover:bg-green-600 rounded-lg transition-all duration-200 shadow-lg"
        >
          <i class="fas fa-download mr-2"></i>Export Data
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div
    class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
  >
    <div
      class="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-100"
    >
      <div
        class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <h3 class="text-xl font-bold text-gray-800">
          <i class="fas fa-filter mr-2 text-indigo-600"></i>Filters & Controls
        </h3>
        <div class="flex flex-col sm:flex-row gap-3">
          <button
            id="applyMAUFilters"
            class="px-6 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 font-medium"
          >
            <i class="fas fa-search mr-2"></i>Apply Filters
          </button>
          <button
            id="clearMAUFilters"
            class="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            <i class="fas fa-times mr-2"></i>Clear
          </button>
        </div>
      </div>
    </div>

    <div class="p-6">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Date Range Filter -->
        <div class="sm:col-span-2 group">
          <label class="block text-sm font-bold text-gray-700 mb-3">
            <i class="fas fa-calendar mr-2 text-indigo-500"></i>Date Range
          </label>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div class="relative">
              <input
                type="text"
                id="mauStartDate"
                class="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-indigo-300"
                placeholder="Start date"
              />
              <i
                class="fas fa-calendar-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-indigo-500 transition-colors"
              ></i>
            </div>
            <div class="relative">
              <input
                type="text"
                id="mauEndDate"
                class="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-indigo-300"
                placeholder="End date"
              />
              <i
                class="fas fa-calendar-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-indigo-500 transition-colors"
              ></i>
            </div>
          </div>
        </div>

        <!-- User Selection Filter -->
        <div class="group">
          <label class="block text-sm font-bold text-gray-700 mb-3">
            <i class="fas fa-user mr-2 text-purple-500"></i>User Filter
          </label>
          <div class="relative">
            <select
              id="mauUserFilter"
              class="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-purple-300 appearance-none bg-white"
            >
              <option value="">All Users</option>
            </select>
            <i
              class="fas fa-user absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-purple-500 transition-colors"
            ></i>
            <i
              class="fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
            ></i>
          </div>
        </div>
      </div>

      <!-- Quick Filters -->
      <div class="mt-6">
        <label class="block text-sm font-bold text-gray-700 mb-3">
          <i class="fas fa-bolt mr-2 text-pink-500"></i>Quick Filters
        </label>
        <div class="flex flex-wrap gap-3">
          <button
            id="thisMonthBtn"
            class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            <i class="fas fa-calendar-day mr-1"></i>This Month
          </button>
          <button
            id="lastMonthBtn"
            class="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl text-sm font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            <i class="fas fa-calendar-minus mr-1"></i>Last Month
          </button>
          <button
            id="last3MonthsBtn"
            class="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl text-sm font-medium hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            <i class="fas fa-calendar-week mr-1"></i>Last 3 Months
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- MAU Metrics Cards -->
  <div
    id="mauMetricsContainer"
    class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6"
  >
    <!-- Active Users Card -->
    <div
      class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-100 text-sm font-medium mb-1">Active Users</p>
          <p id="activeUsersCount" class="text-3xl font-bold">-</p>
          <p class="text-blue-200 text-xs mt-1">In selected period</p>
        </div>
        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
          <i class="fas fa-users text-2xl"></i>
        </div>
      </div>
    </div>

    <!-- Total Requests Card -->
    <div
      class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm font-medium mb-1">Total Requests</p>
          <p id="totalRequestsCount" class="text-3xl font-bold">-</p>
          <p class="text-green-200 text-xs mt-1">API calls made</p>
        </div>
        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
          <i class="fas fa-chart-bar text-2xl"></i>
        </div>
      </div>
    </div>

    <!-- Avg Requests per User Card -->
    <div
      class="bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="text-yellow-100 text-sm font-medium mb-1">Avg per User</p>
          <p id="avgRequestsPerUser" class="text-3xl font-bold">-</p>
          <p class="text-yellow-200 text-xs mt-1">Requests per user</p>
        </div>
        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
          <i class="fas fa-calculator text-2xl"></i>
        </div>
      </div>
    </div>

    <!-- Peak Activity Day Card -->
    <div
      class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="text-purple-100 text-sm font-medium mb-1">Peak Day</p>
          <p id="peakActivityDay" class="text-lg font-bold">-</p>
          <p class="text-purple-200 text-xs mt-1">Most active day</p>
        </div>
        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
          <i class="fas fa-calendar-star text-2xl"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div id="mauChartsContainer" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Daily Active Users Chart -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-chart-line mr-2 text-blue-600"></i>Daily Active Users
      </h3>
      <div class="chart-container">
        <canvas id="dailyActiveUsersChart"></canvas>
      </div>
    </div>

    <!-- Top Users Chart -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-chart-bar mr-2 text-green-600"></i>Top Active Users
      </h3>
      <div class="chart-container">
        <canvas id="topUsersChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Top Users Table -->
  <div
    class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
  >
    <div
      class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200"
    >
      <h3 class="text-lg font-bold text-gray-800">
        <i class="fas fa-trophy mr-2 text-yellow-600"></i>Top Active Users
      </h3>
    </div>
    <div class="overflow-x-auto">
      <table id="topUsersTable" class="min-w-full">
        <thead class="bg-gradient-to-r from-indigo-50 to-purple-50">
          <tr
            class="text-left text-xs font-bold text-gray-700 uppercase tracking-wider"
          >
            <th class="px-6 py-4">
              <div class="flex items-center">
                <i class="fas fa-hashtag mr-2 text-indigo-600"></i>Rank
              </div>
            </th>
            <th class="px-6 py-4">
              <div class="flex items-center">
                <i class="fas fa-user mr-2 text-purple-600"></i>User Email
              </div>
            </th>
            <th class="px-6 py-4">
              <div class="flex items-center">
                <i class="fas fa-chart-bar mr-2 text-green-600"></i>Requests
              </div>
            </th>
            <th class="px-6 py-4">
              <div class="flex items-center">
                <i class="fas fa-clock mr-2 text-orange-600"></i>Last Active
              </div>
            </th>
          </tr>
        </thead>
        <tbody id="topUsersTableBody" class="bg-white divide-y divide-gray-100">
          <!-- Table rows will be populated here -->
        </tbody>
      </table>
    </div>
  </div>
</div>

<script>
  // Initialize MAU Dashboard when loaded
  window.initMAUDashboard = function (token) {
    // MAU Dashboard initialization code will be added here
    console.log(
      "MAU Dashboard initialized with token:",
      token ? "present" : "missing"
    );

    // Load MAU dashboard script
    const script = document.createElement("script");
    script.src = "/static/mau-dashboard.js";
    script.onload = function () {
      if (window.MAUDashboard) {
        window.MAUDashboard.init(token);
      }
    };
    document.head.appendChild(script);
  };
</script>
